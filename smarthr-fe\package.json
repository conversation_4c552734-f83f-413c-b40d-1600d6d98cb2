{"name": "smarthr-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:ui": "playwright test --ui", "test:e2e:chromium": "playwright test --project=chromium", "test:e2e:firefox": "playwright test --project=firefox", "test:e2e:webkit": "playwright test --project=webkit", "test:e2e:mobile": "playwright test --project='Mobile Chrome'", "test:e2e:report": "playwright show-report", "test:e2e:install": "playwright install", "test:visual": "playwright test --project=visual-regression", "test:visual:update": "playwright test --project=visual-regression --update-snapshots", "test:parallel": "playwright test --workers=4", "test:serial": "playwright test --workers=1", "test:analyze": "node -e \"const { TestResultAnalyzer } = require('./tests/utils/test-reporter.ts'); console.log(JSON.stringify(TestResultAnalyzer.analyzeResults('test-results/results.json'), null, 2));\"", "test:docker": "./scripts/run-tests-docker.sh", "test:docker:headed": "./scripts/run-tests-docker.sh --headed", "test:docker:debug": "./scripts/run-tests-docker.sh --debug --workers=1"}, "dependencies": {"@azure/msal-browser": "^3.27.0", "@azure/msal-react": "^2.2.0", "@tanstack/react-query": "^5.59.16", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@vitest/ui": "^3.2.4", "antd": "^5.21.5", "axios": "^1.7.7", "dotenv": "^16.4.7", "jsdom": "^26.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.27.0", "vitest": "^3.2.4"}, "devDependencies": {"@eslint/js": "^9.11.1", "@playwright/test": "^1.55.0", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "dotenv": "^16.4.7", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "playwright": "^1.55.0", "standard": "^16.0.4", "vite": "^5.4.8"}, "eslintConfig": {"extends": "./node_modules/standard/eslintrc.json"}}