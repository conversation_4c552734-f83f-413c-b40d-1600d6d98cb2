import { test, expect } from '../fixtures';
import { createAssertions } from '../utils/assertions';

test.describe('Interview Conduct and Evaluation', () => {
  test('should start an interview session', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { positionId, candidateIds } = setupData;
    
    // Navigate to interviews page or interview details
    await loggedInPage.goto(`/interview/${candidateIds[0]}`);
    await assertions.expectLoadingComplete();
    
    // Look for start interview button
    const startButton = loggedInPage.locator('button:has-text("Start Interview"), .ant-btn:has-text("Start")');
    
    if (await startButton.isVisible()) {
      await startButton.click();
      
      // Should navigate to interview conduct page
      await assertions.expectUrlToMatch(/\/interview\/.*\/conduct/);
      
      // Should see interview interface
      const interviewInterface = loggedInPage.locator('.interview-interface, [data-testid="interview-conduct"]');
      await expect(interviewInterface).toBeVisible();
      
      // Should see questions list
      const questionsList = loggedInPage.locator('.questions-list, .interview-questions');
      await expect(questionsList).toBeVisible();
    } else {
      console.log('Start interview button not found - may require different navigation');
    }
  });

  test('should display interview questions during conduct', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { candidateIds } = setupData;
    
    // Navigate directly to interview conduct page
    await loggedInPage.goto(`/interview/${candidateIds[0]}/conduct`);
    await assertions.expectLoadingComplete();
    
    // Should see interview questions
    const questionsList = loggedInPage.locator('.question-item, .interview-question, .ant-list-item');
    await expect(questionsList.first()).toBeVisible();
    
    // Should have multiple questions
    const questionCount = await questionsList.count();
    expect(questionCount).toBeGreaterThan(0);
    
    // Each question should be readable
    for (let i = 0; i < Math.min(questionCount, 3); i++) {
      const question = questionsList.nth(i);
      const questionText = await question.textContent();
      expect(questionText?.trim()).toBeTruthy();
    }
  });

  test('should record interview notes', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { candidateIds } = setupData;
    
    await loggedInPage.goto(`/interview/${candidateIds[0]}/conduct`);
    await assertions.expectLoadingComplete();
    
    // Look for notes section
    const notesArea = loggedInPage.locator('textarea[placeholder*="notes"], .notes-section textarea, [data-testid="interview-notes"]');
    
    if (await notesArea.isVisible()) {
      // Add some notes
      const testNotes = 'Candidate showed good technical knowledge. Strong communication skills.';
      await notesArea.fill(testNotes);
      
      // Save notes
      const saveButton = loggedInPage.locator('button:has-text("Save"), .ant-btn:has-text("Save")');
      if (await saveButton.isVisible()) {
        await saveButton.click();
        await assertions.expectSuccessNotification();
      }
      
      // Verify notes are saved
      const savedNotes = await notesArea.inputValue();
      expect(savedNotes).toContain(testNotes);
    } else {
      console.log('Notes section not found');
    }
  });

  test('should handle interview transcript upload', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { candidateIds } = setupData;
    
    await loggedInPage.goto(`/interview/${candidateIds[0]}/conduct`);
    await assertions.expectLoadingComplete();
    
    // Look for transcript upload section
    const uploadArea = loggedInPage.locator('.ant-upload, [data-testid="transcript-upload"], input[type="file"]');
    
    if (await uploadArea.isVisible()) {
      // Create a test transcript file
      const testTranscript = 'Interviewer: Tell me about your experience.\nCandidate: I have 5 years of experience in software development...';
      
      // Simulate file upload (this would need actual file handling in real tests)
      const fileInput = loggedInPage.locator('input[type="file"]');
      if (await fileInput.isVisible()) {
        // In a real test, you would upload an actual file
        console.log('File upload functionality detected');
      }
    } else {
      console.log('Transcript upload not available');
    }
  });

  test('should evaluate candidate responses', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { candidateIds } = setupData;
    
    await loggedInPage.goto(`/interview/${candidateIds[0]}/conduct`);
    await assertions.expectLoadingComplete();
    
    // Look for evaluation section
    const evaluationSection = loggedInPage.locator('.evaluation-section, [data-testid="candidate-evaluation"]');
    
    if (await evaluationSection.isVisible()) {
      // Look for rating components
      const ratingComponents = evaluationSection.locator('.ant-rate, .rating-component');
      
      if (await ratingComponents.first().isVisible()) {
        // Give ratings
        const firstRating = ratingComponents.first();
        const stars = firstRating.locator('.ant-rate-star');
        
        if (await stars.count() > 0) {
          // Click on 4th star (4 out of 5 rating)
          await stars.nth(3).click();
        }
      }
      
      // Look for evaluation comments
      const commentsArea = evaluationSection.locator('textarea, .ant-input');
      if (await commentsArea.isVisible()) {
        await commentsArea.fill('Strong technical skills, good problem-solving approach.');
      }
      
      // Save evaluation
      const saveButton = evaluationSection.locator('button:has-text("Save"), .ant-btn:has-text("Save")');
      if (await saveButton.isVisible()) {
        await saveButton.click();
        await assertions.expectSuccessNotification();
      }
    } else {
      console.log('Evaluation section not found');
    }
  });

  test('should complete interview session', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { candidateIds } = setupData;
    
    await loggedInPage.goto(`/interview/${candidateIds[0]}/conduct`);
    await assertions.expectLoadingComplete();
    
    // Look for complete interview button
    const completeButton = loggedInPage.locator('button:has-text("Complete"), .ant-btn:has-text("Finish")');
    
    if (await completeButton.isVisible()) {
      await completeButton.click();
      
      // Should show confirmation dialog
      const confirmDialog = loggedInPage.locator('.ant-modal');
      if (await confirmDialog.isVisible()) {
        const confirmButton = confirmDialog.locator('button:has-text("Confirm"), .ant-btn-primary');
        await confirmButton.click();
      }
      
      // Should show success notification
      await assertions.expectSuccessNotification();
      
      // Should navigate away from conduct page
      await assertions.expectUrlToMatch(/\/interview\/.*(?!\/conduct)/);
    } else {
      console.log('Complete interview button not found');
    }
  });

  test('should display interview summary after completion', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { candidateIds } = setupData;
    
    // Navigate to interview summary/results page
    await loggedInPage.goto(`/interview/${candidateIds[0]}/summary`);
    await assertions.expectLoadingComplete();
    
    // Should see interview summary
    const summarySection = loggedInPage.locator('.interview-summary, [data-testid="interview-summary"]');
    await expect(summarySection).toBeVisible();
    
    // Should show candidate information
    const candidateInfo = loggedInPage.locator('.candidate-info, .candidate-details');
    await expect(candidateInfo).toBeVisible();
    
    // Should show interview results
    const resultsSection = loggedInPage.locator('.interview-results, .evaluation-results');
    if (await resultsSection.isVisible()) {
      // Should have ratings or scores
      const ratings = resultsSection.locator('.ant-rate, .rating, .score');
      await expect(ratings.first()).toBeVisible();
    }
  });

  test('should handle different interview types', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { candidateIds } = setupData;
    
    const interviewTypes = ['technical', 'hr', 'behavioral'];
    
    for (const type of interviewTypes) {
      // Navigate to specific interview type
      await loggedInPage.goto(`/interview/${candidateIds[0]}/conduct?type=${type}`);
      await assertions.expectLoadingComplete();
      
      // Should load appropriate questions for the type
      const questionsList = loggedInPage.locator('.question-item, .interview-question');
      const hasQuestions = await questionsList.count() > 0;
      const hasEmptyState = await loggedInPage.locator('.ant-empty').isVisible();
      
      expect(hasQuestions || hasEmptyState).toBeTruthy();
      
      console.log(`${type} interview type loaded successfully`);
    }
  });

  test('should save interview progress automatically', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { candidateIds } = setupData;
    
    await loggedInPage.goto(`/interview/${candidateIds[0]}/conduct`);
    await assertions.expectLoadingComplete();
    
    // Add some notes
    const notesArea = loggedInPage.locator('textarea[placeholder*="notes"], .notes-section textarea');
    if (await notesArea.isVisible()) {
      await notesArea.fill('Test notes for auto-save');
      
      // Wait a bit for auto-save
      await loggedInPage.waitForTimeout(2000);
      
      // Refresh page
      await loggedInPage.reload();
      await assertions.expectLoadingComplete();
      
      // Notes should be preserved
      const savedNotes = await notesArea.inputValue();
      expect(savedNotes).toContain('Test notes for auto-save');
    }
  });

  test('should handle interview interruptions', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { candidateIds } = setupData;
    
    await loggedInPage.goto(`/interview/${candidateIds[0]}/conduct`);
    await assertions.expectLoadingComplete();
    
    // Add some progress
    const notesArea = loggedInPage.locator('textarea[placeholder*="notes"], .notes-section textarea');
    if (await notesArea.isVisible()) {
      await notesArea.fill('Interview in progress...');
    }
    
    // Navigate away (simulating interruption)
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Navigate back to interview
    await loggedInPage.goto(`/interview/${candidateIds[0]}/conduct`);
    await assertions.expectLoadingComplete();
    
    // Should restore previous state
    if (await notesArea.isVisible()) {
      const restoredNotes = await notesArea.inputValue();
      expect(restoredNotes).toContain('Interview in progress');
    }
  });

  test('should export interview results', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { candidateIds } = setupData;
    
    await loggedInPage.goto(`/interview/${candidateIds[0]}/summary`);
    await assertions.expectLoadingComplete();
    
    // Look for export button
    const exportButton = loggedInPage.locator('button:has-text("Export"), .ant-btn:has-text("Export")');
    
    if (await exportButton.isVisible()) {
      // Test export functionality
      const downloadPromise = loggedInPage.waitForEvent('download');
      await exportButton.click();
      
      try {
        const download = await downloadPromise;
        expect(download.suggestedFilename()).toBeTruthy();
        console.log(`Export file: ${download.suggestedFilename()}`);
      } catch (error) {
        console.log('Export may require additional steps or different interaction');
      }
    } else {
      console.log('Export functionality not available');
    }
  });

  test('should handle interview evaluation errors', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { candidateIds } = setupData;
    
    await loggedInPage.goto(`/interview/${candidateIds[0]}/conduct`);
    await assertions.expectLoadingComplete();
    
    // Simulate network error for saving evaluation
    await loggedInPage.route('**/api/interviews/**', route => route.abort());
    
    // Try to save evaluation
    const saveButton = loggedInPage.locator('button:has-text("Save"), .ant-btn:has-text("Save")');
    if (await saveButton.isVisible()) {
      await saveButton.click();
      
      // Should show error notification
      await assertions.expectErrorNotification();
      
      // Should not crash the application
      await assertions.expectLoadingComplete();
    }
  });

  test('should validate interview completion requirements', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { candidateIds } = setupData;
    
    await loggedInPage.goto(`/interview/${candidateIds[0]}/conduct`);
    await assertions.expectLoadingComplete();
    
    // Try to complete interview without required fields
    const completeButton = loggedInPage.locator('button:has-text("Complete"), .ant-btn:has-text("Finish")');
    
    if (await completeButton.isVisible()) {
      await completeButton.click();
      
      // Should show validation errors or prevent completion
      const validationError = loggedInPage.locator('.ant-form-item-explain-error, .error-message');
      const confirmDialog = loggedInPage.locator('.ant-modal');
      
      const hasValidation = await validationError.isVisible();
      const hasConfirmDialog = await confirmDialog.isVisible();
      
      // Should either show validation or confirmation dialog
      expect(hasValidation || hasConfirmDialog).toBeTruthy();
      
      // Close dialog if it appeared
      if (hasConfirmDialog) {
        const cancelButton = confirmDialog.locator('button:has-text("Cancel"), .ant-btn:not(.ant-btn-primary)');
        if (await cancelButton.isVisible()) {
          await cancelButton.click();
        }
      }
    }
  });
});
