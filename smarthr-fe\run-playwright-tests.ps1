# Script para ejecutar tests de Playwright
Write-Host "=== Ejecutando Tests de Playwright ===" -ForegroundColor Green
Write-Host ""

# Cambiar al directorio correcto
$targetDir = "c:\Users\<USER>\OneDrive\Desktop\ArroyoConsulting\smarthr\smarthr-fe"
Set-Location $targetDir
Write-Host "Directorio actual: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# Verificar que estamos en el directorio correcto
if (Test-Path "playwright.config.ts") {
    Write-Host "✓ Archivo playwright.config.ts encontrado" -ForegroundColor Green
} else {
    Write-Host "✗ No se encontró playwright.config.ts" -ForegroundColor Red
    exit 1
}

# Verificar que node_modules existe
if (Test-Path "node_modules") {
    Write-Host "✓ node_modules encontrado" -ForegroundColor Green
} else {
    Write-Host "✗ node_modules no encontrado. Ejecutando npm install..." -ForegroundColor Yellow
    npm install
}

Write-Host ""
Write-Host "Instalando navegadores de Playwright..." -ForegroundColor Yellow
npx playwright install

Write-Host ""
Write-Host "Ejecutando tests de Playwright..." -ForegroundColor Yellow
npx playwright test --reporter=list

Write-Host ""
Write-Host "=== Tests completados ===" -ForegroundColor Green
