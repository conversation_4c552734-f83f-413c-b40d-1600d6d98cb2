// Test simple para verificar que Playwright funciona
const { test, expect } = require('@playwright/test');

test('Test básico de navegación', async ({ page }) => {
  console.log('Iniciando test básico...');
  
  // Navegar a la aplicación
  await page.goto('http://localhost:5173');
  
  // Verificar que la página carga
  await expect(page).toHaveTitle(/BGSF/);
  
  // Verificar que el heading principal existe
  await expect(page.locator('h3')).toContainText('Job Orders');
  
  // Probar la funcionalidad de búsqueda
  await page.fill('input[placeholder*="Search"]', 'Data Analyst');
  await page.click('button[aria-label="search"], button:has(img[alt="search"])');
  
  // Esperar a que se actualice la tabla
  await page.waitForTimeout(2000);
  
  // Verificar que hay resultados
  const resultsText = await page.locator('text=/\\d+ Job Orders Listed/').textContent();
  console.log('Resultados de búsqueda:', resultsText);
  
  // Probar navegación a candidatos
  await page.click('a[href="/candidates"]');
  await expect(page).toHaveURL(/.*\/candidates/);
  await expect(page.locator('h1')).toContainText('Candidates');
  
  // Volver a la página principal
  await page.click('a[href="/"]');
  await expect(page).toHaveURL(/.*\/$/);
  
  console.log('Test completado exitosamente!');
});

test('Test de búsqueda de trabajos', async ({ page }) => {
  console.log('Iniciando test de búsqueda...');
  
  await page.goto('http://localhost:5173');
  
  // Limpiar campo de búsqueda y buscar algo específico
  await page.fill('input[placeholder*="Search"]', '');
  await page.fill('input[placeholder*="Search"]', 'Python');
  await page.click('button[aria-label="search"], button:has(img[alt="search"])');
  
  await page.waitForTimeout(2000);
  
  // Verificar que hay resultados o mensaje de no resultados
  const hasResults = await page.locator('.ant-table-tbody tr').count() > 0;
  const hasEmptyState = await page.locator('text="No data"').isVisible();
  
  console.log('Tiene resultados:', hasResults);
  console.log('Muestra estado vacío:', hasEmptyState);
  
  expect(hasResults || hasEmptyState).toBeTruthy();
  
  console.log('Test de búsqueda completado!');
});
