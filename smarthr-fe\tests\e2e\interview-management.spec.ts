import { test, expect } from '../fixtures';
import { createAssertions } from '../utils/assertions';
import { JobDetailsPage } from '../pages';

test.describe('Interview Creation and Management', () => {
  test('should generate interview questions for a position', async ({ loggedInPage, createdPositionId }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, createdPositionId);
    
    await jobDetailsPage.goto();
    
    // Navigate to interview tab
    await jobDetailsPage.navigateToTab('interview');
    
    // Look for generate questions button
    const generateButton = loggedInPage.locator('button:has-text("Generate"), .ant-btn:has-text("Generate")');
    
    if (await generateButton.isVisible()) {
      // Click generate questions
      await generateButton.click();
      
      // Should show loading state
      await loggedInPage.waitForSelector('.ant-spin-spinning', { timeout: 5000 });
      
      // Wait for questions to be generated
      await assertions.expectLoadingComplete();
      
      // Should show success notification
      await assertions.expectSuccessNotification();
      
      // Should display generated questions
      const questionsList = loggedInPage.locator('.question-item, .ant-list-item, .interview-question');
      await expect(questionsList.first()).toBeVisible({ timeout: 10000 });
      
      // Should have multiple questions
      const questionCount = await questionsList.count();
      expect(questionCount).toBeGreaterThan(0);
    } else {
      console.log('Generate questions button not found - may require different navigation');
    }
  });

  test('should display generated interview questions', async ({ loggedInPage, positionWithQuestions }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, positionWithQuestions);
    
    await jobDetailsPage.goto();
    
    // Navigate to interview tab
    await jobDetailsPage.navigateToTab('interview');
    
    // Should see generated questions
    const questionsList = loggedInPage.locator('.question-item, .ant-list-item, .interview-question');
    await expect(questionsList.first()).toBeVisible();
    
    // Should have multiple questions
    const questionCount = await questionsList.count();
    expect(questionCount).toBeGreaterThan(0);
    
    // Each question should have content
    for (let i = 0; i < Math.min(questionCount, 3); i++) {
      const question = questionsList.nth(i);
      const questionText = await question.textContent();
      expect(questionText?.trim()).toBeTruthy();
    }
  });

  test('should edit interview questions', async ({ loggedInPage, positionWithQuestions }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, positionWithQuestions);
    
    await jobDetailsPage.goto();
    await jobDetailsPage.navigateToTab('interview');
    
    // Look for edit buttons on questions
    const questionsList = loggedInPage.locator('.question-item, .ant-list-item, .interview-question');
    const firstQuestion = questionsList.first();
    
    const editButton = firstQuestion.locator('button:has-text("Edit"), .ant-btn:has-text("Edit"), .edit-icon');
    
    if (await editButton.isVisible()) {
      await editButton.click();
      
      // Should open edit form/modal
      const editForm = loggedInPage.locator('.ant-modal, .ant-drawer, .edit-form, textarea, input');
      await expect(editForm).toBeVisible();
      
      // Try to modify the question
      const textArea = loggedInPage.locator('textarea, .ant-input');
      if (await textArea.isVisible()) {
        await textArea.fill('Modified interview question for testing');
        
        // Save changes
        const saveButton = loggedInPage.locator('button:has-text("Save"), .ant-btn-primary');
        if (await saveButton.isVisible()) {
          await saveButton.click();
          await assertions.expectSuccessNotification();
        }
      }
      
      // Close modal if still open
      const closeButton = loggedInPage.locator('.ant-modal-close, button:has-text("Cancel")');
      if (await closeButton.isVisible()) {
        await closeButton.click();
      }
    } else {
      console.log('Edit functionality not available or different interaction pattern');
    }
  });

  test('should delete interview questions', async ({ loggedInPage, positionWithQuestions }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, positionWithQuestions);
    
    await jobDetailsPage.goto();
    await jobDetailsPage.navigateToTab('interview');
    
    // Get initial question count
    const questionsList = loggedInPage.locator('.question-item, .ant-list-item, .interview-question');
    const initialCount = await questionsList.count();
    
    if (initialCount > 0) {
      const firstQuestion = questionsList.first();
      const deleteButton = firstQuestion.locator('button:has-text("Delete"), .ant-btn-danger, .delete-icon');
      
      if (await deleteButton.isVisible()) {
        await deleteButton.click();
        
        // Confirm deletion if modal appears
        const confirmButton = loggedInPage.locator('.ant-modal button:has-text("Confirm"), .ant-btn-primary:has-text("Delete")');
        if (await confirmButton.isVisible()) {
          await confirmButton.click();
        }
        
        // Should show success notification
        await assertions.expectSuccessNotification();
        
        // Should have one less question
        const finalCount = await questionsList.count();
        expect(finalCount).toBe(initialCount - 1);
      } else {
        console.log('Delete functionality not available');
      }
    }
  });

  test('should create new interview for candidate', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { positionId, candidateIds } = setupData;
    
    // Navigate to job details
    const jobDetailsPage = new JobDetailsPage(loggedInPage, positionId);
    await jobDetailsPage.goto();
    
    // Navigate to candidates tab
    await jobDetailsPage.navigateToTab('candidates');
    
    // Look for create interview button
    const createInterviewButton = loggedInPage.locator('button:has-text("Create Interview"), .ant-btn:has-text("Interview")');
    
    if (await createInterviewButton.isVisible()) {
      await createInterviewButton.click();
      
      // Should open interview creation form
      const interviewForm = loggedInPage.locator('.ant-modal, .ant-drawer, [data-testid="interview-form"]');
      await expect(interviewForm).toBeVisible();
      
      // Fill interview details
      const interviewTypeSelect = interviewForm.locator('.ant-select:has(.ant-select-selection-item:has-text("Type"))');
      if (await interviewTypeSelect.isVisible()) {
        await interviewTypeSelect.click();
        await loggedInPage.click('.ant-select-item-option:has-text("Technical")');
      }
      
      // Set interview date/time
      const dateInput = interviewForm.locator('.ant-picker, input[type="date"]');
      if (await dateInput.isVisible()) {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const dateString = tomorrow.toISOString().split('T')[0];
        await dateInput.fill(dateString);
      }
      
      // Save interview
      const saveButton = interviewForm.locator('button:has-text("Create"), .ant-btn-primary');
      if (await saveButton.isVisible()) {
        await saveButton.click();
        
        // Should show success notification
        await assertions.expectSuccessNotification();
        
        // Modal should close
        await expect(interviewForm).not.toBeVisible();
      }
    } else {
      console.log('Create interview button not found');
    }
  });

  test('should display interview list for position', async ({ loggedInPage, setupData }) => {
    const assertions = createAssertions(loggedInPage);
    const { positionId } = setupData;
    
    const jobDetailsPage = new JobDetailsPage(loggedInPage, positionId);
    await jobDetailsPage.goto();
    
    // Navigate to interviews tab or section
    await jobDetailsPage.navigateToTab('interview');
    
    // Should see interviews list or empty state
    const interviewsList = loggedInPage.locator('.interview-list, .ant-list, .ant-table');
    const emptyState = loggedInPage.locator('.ant-empty');
    
    const hasInterviews = await interviewsList.isVisible();
    const hasEmptyState = await emptyState.isVisible();
    
    expect(hasInterviews || hasEmptyState).toBeTruthy();
  });

  test('should handle question generation errors', async ({ loggedInPage, createdPositionId }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, createdPositionId);
    
    await jobDetailsPage.goto();
    await jobDetailsPage.navigateToTab('interview');
    
    // Simulate network error for question generation
    await loggedInPage.route('**/api/questions/**', route => route.abort());
    await loggedInPage.route('**/api/generate/**', route => route.abort());
    
    const generateButton = loggedInPage.locator('button:has-text("Generate"), .ant-btn:has-text("Generate")');
    
    if (await generateButton.isVisible()) {
      await generateButton.click();
      
      // Should show error notification
      await assertions.expectErrorNotification();
      
      // Should not crash the application
      await assertions.expectLoadingComplete();
    }
  });

  test('should validate interview question content', async ({ loggedInPage, positionWithQuestions }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, positionWithQuestions);
    
    await jobDetailsPage.goto();
    await jobDetailsPage.navigateToTab('interview');
    
    // Get all questions
    const questionsList = loggedInPage.locator('.question-item, .ant-list-item, .interview-question');
    const questionCount = await questionsList.count();
    
    if (questionCount > 0) {
      // Validate each question has meaningful content
      for (let i = 0; i < Math.min(questionCount, 5); i++) {
        const question = questionsList.nth(i);
        const questionText = await question.textContent();
        
        // Question should not be empty
        expect(questionText?.trim()).toBeTruthy();
        
        // Question should be reasonably long (more than just a few words)
        expect(questionText?.trim().length).toBeGreaterThan(10);
        
        // Question should contain question words or end with question mark
        const hasQuestionIndicators = questionText?.includes('?') || 
                                    questionText?.toLowerCase().includes('what') ||
                                    questionText?.toLowerCase().includes('how') ||
                                    questionText?.toLowerCase().includes('why') ||
                                    questionText?.toLowerCase().includes('describe') ||
                                    questionText?.toLowerCase().includes('explain');
        
        expect(hasQuestionIndicators).toBeTruthy();
      }
    }
  });

  test('should handle different question types', async ({ loggedInPage, createdPositionId }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, createdPositionId);
    
    await jobDetailsPage.goto();
    await jobDetailsPage.navigateToTab('interview');
    
    // Look for question type filters or categories
    const questionTypes = ['Technical', 'Behavioral', 'General', 'HR'];
    
    for (const type of questionTypes) {
      const typeFilter = loggedInPage.locator(`button:has-text("${type}"), .ant-btn:has-text("${type}")`);
      
      if (await typeFilter.isVisible()) {
        await typeFilter.click();
        await assertions.expectLoadingComplete();
        
        // Should show questions of that type
        const questionsList = loggedInPage.locator('.question-item, .ant-list-item, .interview-question');
        const hasQuestions = await questionsList.count() > 0;
        const hasEmptyState = await loggedInPage.locator('.ant-empty').isVisible();
        
        expect(hasQuestions || hasEmptyState).toBeTruthy();
      }
    }
  });

  test('should regenerate interview questions', async ({ loggedInPage, positionWithQuestions }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, positionWithQuestions);
    
    await jobDetailsPage.goto();
    await jobDetailsPage.navigateToTab('interview');
    
    // Get initial questions
    const questionsList = loggedInPage.locator('.question-item, .ant-list-item, .interview-question');
    const initialQuestions = [];
    
    const initialCount = await questionsList.count();
    for (let i = 0; i < Math.min(initialCount, 3); i++) {
      const questionText = await questionsList.nth(i).textContent();
      initialQuestions.push(questionText);
    }
    
    // Look for regenerate button
    const regenerateButton = loggedInPage.locator('button:has-text("Regenerate"), .ant-btn:has-text("Regenerate")');
    
    if (await regenerateButton.isVisible()) {
      await regenerateButton.click();
      
      // Should show loading
      await loggedInPage.waitForSelector('.ant-spin-spinning', { timeout: 5000 });
      await assertions.expectLoadingComplete();
      
      // Should show success notification
      await assertions.expectSuccessNotification();
      
      // Questions should be different (at least some of them)
      const newQuestions = [];
      const newCount = await questionsList.count();
      
      for (let i = 0; i < Math.min(newCount, 3); i++) {
        const questionText = await questionsList.nth(i).textContent();
        newQuestions.push(questionText);
      }
      
      // At least one question should be different
      const hasChanges = newQuestions.some((q, i) => q !== initialQuestions[i]);
      expect(hasChanges).toBeTruthy();
    } else {
      console.log('Regenerate functionality not available');
    }
  });
});
