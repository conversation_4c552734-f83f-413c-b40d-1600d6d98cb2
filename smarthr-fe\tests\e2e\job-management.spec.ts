import { test, expect } from '../fixtures';
import { createAssertions } from '../utils/assertions';
import { JobOrdersPage, JobDetailsPage } from '../pages';

test.describe('Job Management', () => {
  test('should display jobs list on home page', async ({ jobOrdersPage }) => {
    const assertions = createAssertions(jobOrdersPage.page);
    
    await jobOrdersPage.goto();
    
    // Should see the jobs table
    await expect(jobOrdersPage.jobTable).toBeVisible();
    
    // Should have at least the table structure even if empty
    await assertions.expectLoadingComplete();
    
    // Check if there are jobs or empty state
    const hasJobs = await jobOrdersPage.jobRows.count() > 0;
    const hasEmptyState = await jobOrdersPage.page.locator('.ant-empty').isVisible();
    
    expect(hasJobs || hasEmptyState).toBeTruthy();
  });

  test('should search for jobs', async ({ jobOrdersPage, createdPositionId }) => {
    const assertions = createAssertions(jobOrdersPage.page);
    
    await jobOrdersPage.goto();
    await assertions.expectLoadingComplete();
    
    // Get initial job count
    const initialCount = await jobOrdersPage.getJobCount();
    
    if (initialCount > 0) {
      // Get the first job name to search for
      const jobNames = await jobOrdersPage.getJobNames();
      const firstJobName = jobNames[0];
      
      // Search for the job
      await jobOrdersPage.searchForJob(firstJobName);
      
      // Should show filtered results
      await jobOrdersPage.expectJobToBeVisible(firstJobName);
      
      // Clear search
      await jobOrdersPage.clearSearch();
      
      // Should show all jobs again
      const finalCount = await jobOrdersPage.getJobCount();
      expect(finalCount).toBeGreaterThanOrEqual(initialCount);
    } else {
      console.log('No jobs available for search test');
    }
  });

  test('should filter jobs by different criteria', async ({ jobOrdersPage }) => {
    const assertions = createAssertions(jobOrdersPage.page);
    
    await jobOrdersPage.goto();
    await assertions.expectLoadingComplete();
    
    const initialCount = await jobOrdersPage.getJobCount();
    
    if (initialCount > 0) {
      // Try filtering by stage if filter exists
      const stageFilter = jobOrdersPage.stageFilter;
      if (await stageFilter.isVisible()) {
        await jobOrdersPage.filterByStage('Active');
        await jobOrdersPage.applyFilters();
        
        // Should show filtered results
        await assertions.expectLoadingComplete();
        
        // Clear filters
        await jobOrdersPage.clearFilters();
        await assertions.expectLoadingComplete();
      }
      
      // Try filtering by client if filter exists
      const clientFilter = jobOrdersPage.clientFilter;
      if (await clientFilter.isVisible()) {
        // Get available clients from dropdown
        await clientFilter.click();
        const clientOptions = jobOrdersPage.page.locator('.ant-select-item-option');
        const optionCount = await clientOptions.count();
        
        if (optionCount > 0) {
          const firstOption = await clientOptions.first().textContent();
          await clientOptions.first().click();
          
          await jobOrdersPage.applyFilters();
          await assertions.expectLoadingComplete();
          
          // Clear filters
          await jobOrdersPage.clearFilters();
        } else {
          // Close dropdown if no options
          await jobOrdersPage.page.keyboard.press('Escape');
        }
      }
    } else {
      console.log('No jobs available for filter test');
    }
  });

  test('should navigate to job details', async ({ jobOrdersPage, loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    await jobOrdersPage.goto();
    await assertions.expectLoadingComplete();
    
    const jobCount = await jobOrdersPage.getJobCount();
    
    if (jobCount > 0) {
      const jobNames = await jobOrdersPage.getJobNames();
      const firstJobName = jobNames[0];
      
      // Click on the job row
      await jobOrdersPage.clickJobRow(firstJobName);
      
      // Should navigate to job details
      await assertions.expectUrlToMatch(/\/job\/[^\/]+/);
      
      // Should see job details content
      const jobDetailsContent = loggedInPage.locator('.ant-card, [data-testid="job-details"], .ant-tabs');
      await expect(jobDetailsContent).toBeVisible();
    } else {
      console.log('No jobs available for navigation test');
    }
  });

  test('should handle pagination in jobs list', async ({ jobOrdersPage }) => {
    const assertions = createAssertions(jobOrdersPage.page);
    
    await jobOrdersPage.goto();
    await assertions.expectLoadingComplete();
    
    // Check if pagination exists
    const pagination = jobOrdersPage.page.locator('.ant-pagination');
    
    if (await pagination.isVisible()) {
      const nextButton = pagination.locator('.ant-pagination-next');
      const isNextEnabled = await nextButton.isEnabled();
      
      if (isNextEnabled) {
        // Go to next page
        await nextButton.click();
        await assertions.expectLoadingComplete();
        
        // Should show different jobs or same jobs on next page
        await jobOrdersPage.expectTableToHaveRows(0); // At least 0 rows (could be empty)
        
        // Go back to first page
        const prevButton = pagination.locator('.ant-pagination-prev');
        if (await prevButton.isEnabled()) {
          await prevButton.click();
          await assertions.expectLoadingComplete();
        }
      }
    } else {
      console.log('No pagination available - all jobs fit on one page');
    }
  });

  test('should display job information correctly', async ({ jobOrdersPage, createdPositionId }) => {
    const assertions = createAssertions(jobOrdersPage.page);
    
    await jobOrdersPage.goto();
    await assertions.expectLoadingComplete();
    
    const jobCount = await jobOrdersPage.getJobCount();
    
    if (jobCount > 0) {
      const jobNames = await jobOrdersPage.getJobNames();
      const firstJobName = jobNames[0];
      
      // Get job details from the table
      const jobDetails = await jobOrdersPage.getJobDetails(firstJobName);
      
      // Should have job information
      expect(Object.keys(jobDetails).length).toBeGreaterThan(0);
      
      // Job name should not be empty
      expect(jobDetails.column_0 || firstJobName).toBeTruthy();
    } else {
      console.log('No jobs available for information display test');
    }
  });

  test('should handle empty jobs list', async ({ jobOrdersPage }) => {
    const assertions = createAssertions(jobOrdersPage.page);
    
    await jobOrdersPage.goto();
    await assertions.expectLoadingComplete();
    
    // Search for something that doesn't exist
    await jobOrdersPage.searchForJob('NonExistentJobXYZ123');
    
    // Should show empty state or no results
    const hasEmptyState = await jobOrdersPage.page.locator('.ant-empty, .ant-table-placeholder').isVisible();
    const hasNoRows = await jobOrdersPage.jobRows.count() === 0;
    
    expect(hasEmptyState || hasNoRows).toBeTruthy();
    
    // Clear search to restore normal state
    await jobOrdersPage.clearSearch();
  });

  test('should refresh jobs list', async ({ jobOrdersPage }) => {
    const assertions = createAssertions(jobOrdersPage.page);
    
    await jobOrdersPage.goto();
    await assertions.expectLoadingComplete();
    
    const initialCount = await jobOrdersPage.getJobCount();
    
    // Refresh the page
    await jobOrdersPage.page.reload();
    await assertions.expectLoadingComplete();
    
    // Should still show jobs
    const finalCount = await jobOrdersPage.getJobCount();
    expect(finalCount).toBe(initialCount);
  });
});

test.describe('Job Details', () => {
  test('should display job details correctly', async ({ loggedInPage, createdPositionId }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, createdPositionId);
    
    await jobDetailsPage.goto();
    
    // Should see job details content
    await expect(jobDetailsPage.jobTitle).toBeVisible();
    
    // Should have tabs for different sections
    const tabs = loggedInPage.locator('.ant-tabs-tab');
    const tabCount = await tabs.count();
    expect(tabCount).toBeGreaterThan(0);
  });

  test('should navigate between job detail tabs', async ({ loggedInPage, createdPositionId }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, createdPositionId);
    
    await jobDetailsPage.goto();
    
    // Navigate to different tabs
    const availableTabs = ['description', 'matchings', 'interview', 'candidates'] as const;
    
    for (const tabName of availableTabs) {
      try {
        await jobDetailsPage.navigateToTab(tabName);
        await assertions.expectTabActive(tabName);
        await assertions.expectLoadingComplete();
      } catch (error) {
        console.log(`Tab ${tabName} not available or not clickable`);
      }
    }
  });

  test('should display job description', async ({ loggedInPage, createdPositionId }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, createdPositionId);
    
    await jobDetailsPage.goto();
    
    // Navigate to description tab
    await jobDetailsPage.navigateToTab('description');
    
    // Should see job description content
    const descriptionContent = loggedInPage.locator('.job-description, [data-testid="job-description"], .ant-card');
    await expect(descriptionContent).toBeVisible();
  });

  test('should handle job not found', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, 'non-existent-job-id');
    
    // Try to navigate to non-existent job
    await loggedInPage.goto('/job/non-existent-job-id');
    await assertions.expectLoadingComplete();
    
    // Should show error state or redirect
    const hasError = await loggedInPage.locator('.ant-result-404, .not-found, .error').isVisible();
    const redirected = !loggedInPage.url().includes('non-existent-job-id');
    
    expect(hasError || redirected).toBeTruthy();
  });
});
